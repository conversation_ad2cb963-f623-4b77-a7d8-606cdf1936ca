rule-anchor:
  # 订阅
  P: &P {type: http, interval: 86400, health-check: {enable: true, interval: 300}, header: {User-Agent:["Clash", "Clash.meta","ClashMeta"]}, exclude-filter:"官网|剩余|流量|套餐|订阅|全球直连|GB|Expire Date|Traffic|ExpireDate", proxy: DIRECT, override: {udp: true, down: "200 Mbps", up: "100 Mbps", skip-cert-verify: true}}
  # DNS
  dns_default: &dns_default [*********, ************]
  dns_cn: &dns_cn ["https://doh.pub/dns-query", "https://dns.alidns.com/dns-query"]
  dns_abroad: &dns_abroad ["https://dns.google/dns-query", "https://cloudflare-dns.com/dns-query"]
  dns_direct: &dns_direct ["***********"] # 需要根据实际修改
  
  # 节点选择
  S1: &S1 {type: select, proxies: [默认代理, 香港自动, 日本自动, 韩国自动, 美国自动, DIRECT]}	# 全部手动
  # 节点筛选
  HK: &HK "(?i)港|hk|hongkong|hong kong" #"(?=.*(港|HK|(?i)Hong))^((?!(台|日|韩|新|美)).)*$"
  JP: &JP "(?i)日本|jp|japan" #"(?=.*(日|JP|(?i)Japan))^((?!(港|台|韩|新|美)).)*$"
  US: &US "(?i)美|us|unitedstates|united states" #"(?=.*(美|US|(?i)States|America))^((?!(港|台|日|韩|新)).)*$"
  KR: &KR "(?i)韩|kr|korea|south korea" #"(?=.*(韩|KR|(?i)Korea))^((?!(港|台|日|新|美)).)*$"
  Exclude: &Exclude "^((?!(官网|剩余|流量|套餐|订阅|全球直连|GB|Expire Date|Traffic|ExpireDate)).)*$"
  # 策略组
  SD: &SD {type: select, include-all: true}
  # fallback 将按照 url 测试结果按照节点顺序选择
  FB: &FB {type: fallback, include-all: true, interval: 60, lazy: true} # , url: "http://www.apple.com/library/test/success.html"
  # url-test 将按照 url 测试结果使用延迟最低节点
  URL: &URL {type: url-test, include-all: true, tolerance: 50, interval: 300, lazy: true}
  # load-balance 将按照算法随机选择节点
  LB: &LB {type: load-balance, include-all: true, interval: 300, strategy: round-robin}
  # 指定某个机场, use: [SubStore] <--> include-all: true  使用proxies中定义的全部节点
  # ICON
  Transfer: &Transfer "https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Transfer.png"
  Auto: &Auto "https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Auto_Link.png"
  All: &All "https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Global.png"
  Default: &Default "https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Flight.png"
  # 规则集
  # class: &class {type: file, interval: 864000, behavior: classical, format: yaml}
  ip: &ip {type: http, interval: 86400, behavior: ipcidr, format: mrs}
  domain: &domain {type: http, interval: 86400, behavior: domain, format: mrs}
  domain_text: &domain_text {type: http, interval: 86400, behavior: domain, format: text}	# list结尾的文件
  # mrs目前 behavior 仅支持 domain/ipcidr  可以通过mihomo convert-ruleset domain/ipcidr yaml/text XXX.yaml XXX.mrs转换得到
  
proxy-providers:
  SubStore: {<<: *P, url: "https://subapi.proxygo.de/share/file/mihomo?token=2RcX5VjR6zLRKv3Ufyqhm",override: {udp: true}}
  
proxies:
  # - {name: "全球直连", type: "direct"}
  # - {name: 链式代理,}

# -----------------------------全局配置-----------------------------
port: 7890 # 仅HTTP协议专用端口（如果你的软件只支持HTTP代理，用这个）
socks-port: 7891
redir-port: 7892 # 仅SOCKS5协议专用端口（如果你的软件只支持SOCKS5代理，用这个）
mixed-port: 7893	# 通用代理端口：同时支持HTTP和SOCKS5协议，一般软件都可以使用这个端口
tproxy-port: 7894
allow-lan: true # 是否允许来自局域网的连接（开启后，其他设备可以通过你的电脑上网）
bind-address: "*"	# 监听地址，"*"表示监听所有地址，这样局域网内其他设备才能连接
mode: rule # 默认就是rule
ipv6: true		# 内核是否支持ipv6≠能用
unified-delay: false	# 降低延迟,为了测试结果好看些
experimental:
    ignore-resolve-fail: true
tcp-concurrent: true	# tcp并发连接,保持默认开启
log-level: warning # 日志设置:silent=不显示日志,info=显示基本信息,warning=显示警告 error=只显示错误,debug=显示所有调试信息(日志会占用内存,一般保持静默即可)
find-process-mode: off # 匹配进程 always/strict/off,路由器用的话off就行,因为没进程可以匹配。strict为内核自动匹配进程
cfw-latency-timeout: 5000
cfw-latency-url: 'http://www.apple.com/library/test/success.html'
cfw-conn-break-strategy: true # 启用后，当当前连接中断时，Clash会自动尝试使用其他可用节点
# interface-name: eth0 # 路由器下根据情况指定出站接口,如clash的ip地址***********,别用eth0,网卡会变化,电脑端不需要
global-client-fingerprint: chrome # 规避某些网站对代理工具的检测,使代理连接看起来像是从Chrome浏览器发出的
keep-alive-idle: 600  # tcp保持连接,减少重连频率,提高连接稳定性
keep-alive-interval: 15   # 设置发送保活探测包的间隔为15秒
disable-keep-alive: false	# 为true时,表示禁用keep-alive-idle和keep-alive-interval
profile:  # 策略组选择和fakeip缓存
    store-selected: true    # 选择节点后,重启内核能继续保持该节点的选择
    store-fake-ip: true     # 保存fake-ip模式下的IP映射缓存,减少DNS解析次数,提高访问速度。重启后继续使用之前的域名到IP的映射关系
    
# -----------------------------控制面板-----------------------------
#external-controller: 0.0.0.0:9090   # 127.0.0.1则只允许本机访问控制面板,0.0.0.0的话,局域网所有设备都能访问.如果做了外网映射,可以用固定ip
#secret: "Yyh7991"    # 控制面板密码,公网暴露在外面的话则需要设置下
#external-ui: "/etc/mihomo/run/ui"   # 存放web界面文件的下载路径
#external-ui-name: zashboard	# 路径下面再建个文件夹,区分不同面板用的
#external-ui-url: "https://mirror.ghproxy.com/https://github.com/Zephyruso/zashboard/archive/refs/heads/gh-pages.zip"    # Clash控制面板web界面文件的存放目录

# -----------------------------嗅探-----------------------------
sniffer:
  enable: true
  sniff:
    HTTP:
      ports: [80, 8080-8880]
      override-destination: true
    TLS:
      ports: [443, 8443]
    QUIC:
      ports: [443, 8443]
  force-domain:	# 强制嗅探
    - "+.v2ex.com"
  skip-domain:	# 不想嗅探.中国大陆的域名不需要嗅探就都写上
    - "rule-set:private_domain,cn_domain"
    - "dlg.io.mi.com"
    - "+.push.apple.com"
    - "+.apple.com"
    - "+.wechat.com"
    - "+.qpic.cn"
    - "+.qq.com"
    - "+.wechatapp.com"
    - "+.vivox.com"
    - "+.oray.com"
    - "+.sunlogin.net"
    - "+.msftconnecttest.com"
    - "+.msftncsi.com"

# -----------------------------流量入站-----------------------------
tun:
  enable: true    # 开启后所有流量都会通过Clash,无需在软件里单独设置代理
  stack: mixed    # system/gvisor/mixed, 出现兼容性问题就选system,正常用mixed或gvisor
  dns-hijack: ["any:53", "tcp://any:53"] # 接管所有DNS请求,防止DNS泄露
  auto-route: true # ROS有自己的路由系统,让Clash接管路由可能导致冲突
  auto-redirect: true
  auto-detect-interface: true # 是否自动识别网卡

# -----------------------------DNS设置-----------------------------
dns:
  enable: true
  listen: 0.0.0.0:53
  ipv6: false
  respect-rules: true
  enhanced-mode: fake-ip
  fake-ip-range: ********/8
  fake-ip-filter-mode: blacklist
  fake-ip-filter:
      - "rule-set:private_domain,cn_domain"	# private_domain私有ip域名,中国大陆域名
      - "*.lan"
      - "*.localdomain"
      - "*.example"
      - "*.invalid"
      - "*.localhost"
      - "*.test"
      - "*.local"
      - "*.home.arpa"
      - "time.*.com"
      - "time.*.gov"
      - "time.*.edu.cn"
      - "time.*.apple.com"
      - "time1.*.com"
      - "time2.*.com"
      - "time3.*.com"
      - "time4.*.com"
      - "time5.*.com"
      - "time6.*.com"
      - "time7.*.com"
      - "ntp.*.com"
      - "ntp1.*.com"
      - "ntp2.*.com"
      - "ntp3.*.com"
      - "ntp4.*.com"
      - "ntp5.*.com"
      - "ntp6.*.com"
      - "ntp7.*.com"
      - "*.time.edu.cn"
      - "*.ntp.org.cn"
      - "+.apple.com"
      - "+.pool.ntp.org"
      - "+.services.googleapis.cn"	# 谷歌商店很多下载不下来的,可以加上
      - "+.xn--ngstr-1ra8j.com"
      - "+.ntp.tencent.com"
      - "+.ntp1.aliyun.com"
      - "+.ntp.ntsc.ac.cn"
      - "+.cn.ntp.org.cn"
      - "time1.cloud.tencent.com"
      - "music.163.com"
      - "*.music.163.com"
      - "*.126.net"
      - "musicapi.taihe.com"
      - "music.taihe.com"
      - "songsearch.kugou.com"
      - "trackercdn.kugou.com"
      - "*.kuwo.cn"
      - "api-jooxtt.sanook.com"
      - "api.joox.com"
      - "joox.com"
      - "y.qq.com"
      - "*.y.qq.com"
      - "streamoc.music.tc.qq.com"
      - "mobileoc.music.tc.qq.com"
      - "isure.stream.qqmusic.qq.com"
      - "dl.stream.qqmusic.qq.com"
      - "aqqmusic.tc.qq.com"
      - "amobile.music.tc.qq.com"
      - "*.xiami.com"
      - "*.music.migu.cn"
      - "music.migu.cn"
      - "*.msftconnecttest.com"
      - "*.msftncsi.com"
      - "msftconnecttest.com"
      - "msftncsi.com"
      - "localhost.ptlogin2.qq.com"
      - "localhost.sec.qq.com"
      - "+.srv.nintendo.net"
      - "+.stun.playstation.net"
      - "*.microsoft.com"
      - "xbox.*.microsoft.com"
      - "xnotify.xboxlive.com"
      - "+.battlenet.com.cn"
      - "+.wotgame.cn"
      - "+.wggames.cn"
      - "+.wowsgame.cn"
      - "+.wargaming.net"
      - "proxy.golang.org"
      - "stun.*.*"
      - "stun.*.*.*"
      - "+.stun.*.*"
      - "+.stun.*.*.*"
      - "+.stun.*.*.*.*"
      - "*.mcdn.bilivideo.cn"
  default-nameserver:
    *dns_default
  proxy-server-nameserver:
    *dns_cn
  # namesever尽量用运营商提供的DNS
  nameserver:
    *dns_default

  # direct-nameserver: # 所有定义了直连的规则,比如访问百度走的是直连,那百度就用这个dns服务器去解析
  #   *dns_cn
  # direct-nameserver-follow-policy: false # 默认false,如果改为true,会提升nameserver-policy优先级,那direct-nameserver作用就不太大了(优先级低于nameserver-policy,DNS的规则)
  # nameserver-policy: # dns的匹配规则,告诉mihomo,访问什么就用什么dns服务器
  #   "+.baidu.com": "114.114.114.114" # "域名":"dns服务器"
  #   "rule-set:private_domain,cn_domain": # 这个规则集里的域名则用下面的服务器
  #     *dns_cn
  #   "rule-set:gfw_domain,geolocation-!cn": # 这个规则集里的域名则用下面的服务器
  #     - tls://8.8.4.4# 默认代理 # 加了#后,表示dns服务器连接的时候走代理
  #     - tls://1.1.1.1# 默认代理&h3=true # 不仅走代理还打开http3的模式
  # # 国外DNS服务器
  # fallback: # 可删除, 请求国外域名的部分就用这个,是老clash内核中为了防止dns污染所做的一个参数.但通常分流规则已经做的非常好,外加有DNS规则(nameserver-policy)加持,则fallback和fallback-filter这些参数就没必要了,反而复杂了dns的解析流程
  #   *dns_abroad
      
  # # 作用：当 Clash 使用默认的 nameserver 进行 DNS 查询后，根据查询结果（主要是 IP 地址）来判断这个结果是否"可信"
  # # 查询结果匹配了 fallback-filter 中的任何规则（geoip, ipcidr, geosite, domain）：Clash 认为 nameserver 返回的结果是可信的（或者是不应使用 fallback 处理的情况）
  # fallback-filter: # 可删除
  #     geoip: true # 只要是大陆的部分就认为没dns污染,采用nameserver
  #     geoip-code: CN # 只要是大陆的部分就认为没dns污染,采用nameserver
  #     geosite: # 匹配到这个就认为有dns污染, 采用fallback
  #       - gfw
  #     ipcidr: # 匹配到这个就认为有dns污染, 采用fallback
  #       # 以下是内网IP段，不会使用国外DNS
  #       - 0.0.0.0/8
  #       - 10.0.0.0/8
  #       - **********/10
  #       - *********/8
  #       - ***********/16
  #       - **********/12
  #       - *********/24
  #       - *********/24
  #       - ***********/24
  #       - ***********/16
  #       - **********/15
  #       - ************/24
  #       - ***********/24
  #       - *********/4
  #       - 240.0.0.0/4
  #       - ***************/32
  #       - ::/128
  #       - ::1/128
  #       - ::ffff:0:0/96
  #       - 64:ff9b::/96
  #       - 100::/64
  #       - 2001::/32
  #       - 2001:20::/28
  #       - 2001:db8::/32
  #       - 2002::/16
  #       - fc00::/7
  #       - fe80::/10
  #       - ff00::/8
  #     # 这些网站强制使用国外DNS解析，避免被污染
  #     domain: # 匹配到这个就认为有dns污染, 采用fallback
  #       - "+.google.com"
  #       - "+.facebook.com"
  #       - "+.youtube.com"
  #       - "+.githubusercontent.com"
  #       - "+.googlevideo.com"
# -----------------------------出站策略-----------------------------
# 先定义下面这部分,把节点筛选出来,然后定义规则匹配
proxy-groups:
    # 按媒体分组
    # 按地区分组
    - {name: 全部手动, <<: *S1, icon: *All}
    - {name: 默认代理, type: load-balance, proxies: [香港自动, 日本自动, 美国自动], interval: 300, strategy: round-robin,icon: *Default} # 可以filter: "^((?!(直连)).)*$"  filter掉送中的节点
    - {name: 香港自动, <<: *LB, filter: *HK, icon: "https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Hong_Kong.png"}
    - {name: 日本自动, <<: *URL, filter: *JP, icon: "https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/Japan.png"}
    - {name: 韩国自动, <<: *URL, filter: *KR, icon: "https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/South_Korea.png"}
    - {name: 美国自动, <<: *URL, filter: *US, icon: "https://raw.githubusercontent.com/Vbaethon/HOMOMIX/main/Icon/Color/USA.png"}
    # - {name: 链式代理, dialer-proxy: 默认代理, 落地节点信息..(type,server,port...)} # 落地节点经过谁中转(这边是默认代理)

# -----------------------------规则集-----------------------------
rule-providers:	# 主参数
  # 规则集名字: { <<: 参数, url:"规则集下载链接"}
  private_domain: { <<: *domain, url: "https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/private.mrs"}
  # proxylite: {<<: *class, url: "https://raw.githubusercontent.com/qichiyuhub/rule/refs/heads/master/ProxyLite.list"} # 示例:list规则集的添加
  ai: { <<: *domain, url: "https://github.com/MetaCubex/meta-rules-dat/raw/refs/heads/meta/geo/geosite/category-ai-chat-!cn.mrs"}
  youtube_domain: {<<: *domain, url: "https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/youtube.mrs"}
  google_domain: {<<: *domain, url: "https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/google.mrs"}
  github_domain: {<<: *domain, url: "https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/github.mrs"}
  telegram_domain: {<<: *domain, url: "https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/telegram.mrs"}
  netflix_domain: {<<: *domain, url: "https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/netflix.mrs"}
  paypal_domain: {<<: *domain, url: "https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/paypal.mrs"}
  onedrive_domain: {<<: *domain, url: "https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/onedrive.mrs"}
  microsoft_domain: {<<: *domain, url: "https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/microsoft.mrs"}
  apple_domain: {<<: *domain, url: "https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/apple-cn.mrs"}
  speedtest_domain: {<<: *domain, url: "https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/ookla-speedtest.mrs"}
  tiktok_domain: {<<: *domain, url: "https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/tiktok.mrs"}
  gfw_domain: {<<: *domain, url: "https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/gfw.mrs"}
  geolocation-!cn: {<<: *domain, url: "https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geosite/geolocation-!cn.mrs"}
  cn_domain: {<<: *domain, url: "https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geosite/cn.mrs"}

  private_ip: { <<: *ip, url: "https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geoip/private.mrs"}
  cn_ip: {<<: *ip, url: "https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geoip/cn.mrs"}
  google_ip: {<<: *ip, url: "https://raw.githubusercontent.com/Metacubex/meta-rules-dat/meta/geo/geoip/google.mrs"}
  telegram_ip: {<<: *ip, url: "https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geoip/telegram.mrs"}
  netflix_ip: {<<: *ip, url: "https://raw.githubusercontent.com/MetaCubex/meta-rules-dat/meta/geo/geoip/netflix.mrs"}
  
# -----------------------------规则匹配-----------------------------
rules:
  # - DOMAIN-SUFFIX, qichiyu.com, 默认代理	# 自定义某个网站域名
  # - SRC-IP-CIDR, *************/32, 直连	# 自定义某台设备的IP,走直连
  - RULE-SET, private_domain, DIRECT
  - RULE-SET, private_ip, DIRECT
  - RULE-SET, apple_domain, DIRECT
  - RULE-SET, ai, 美国自动
  - RULE-SET, github_domain, 默认代理
  - RULE-SET, youtube_domain, 默认代理 
  - RULE-SET, google_domain, 默认代理
  - RULE-SET, onedrive_domain, DIRECT
  - RULE-SET, microsoft_domain, DIRECT
  - RULE-SET, tiktok_domain, 默认代理
  - RULE-SET, speedtest_domain, 默认代理
  - RULE-SET, telegram_domain, 默认代理
  - RULE-SET, netflix_domain, 默认代理
  - RULE-SET, paypal_domain, 默认代理
  - RULE-SET, gfw_domain, 默认代理
  - RULE-SET, geolocation-!cn, 默认代理
  - RULE-SET, cn_domain, DIRECT
  - RULE-SET, google_ip, 默认代理, no-resolve	# 碰到ip规则,不加no-resolve就会开始DNS解析了
  - RULE-SET, netflix_ip, 默认代理, no-resolve
  - RULE-SET, telegram_ip, 默认代理, no-resolve
  - RULE-SET, cn_ip, DIRECT	# 大陆ip会进行解析匹配,要是这个再不匹配,下面就直接走代理了,那就会造成很多中国网站全部走了国外解析/国外代理,从外国绕了一圈再回来,不值得.
  - MATCH, 默认代理	# 兜底规则,走代理就行